from datetime import datetime
import backtrader as bt
import pandas as pd
import backtrader.analyzers as btanalyzers
import matplotlib.pyplot as plt
import numpy as np
import pyfolio as pf
import math
from warnings import simplefilter
from pytz import timezone
import talib
from scipy.stats import zscore
import quantstats as qs


tz = timezone('Asia/Shanghai')

simplefilter(action='ignore', category=FutureWarning)
simplefilter(action='ignore', category=UserWarning)


class PandasData_more(bt.feeds.PandasData):
    lines = ('z_score', 't_score', 'corr', 'btc_eth')  # 要添加的线
    # 设置 line 在数据源上的列位置
    params = (
        ('z_score', -1),
        ('t_score', -1),
        ('corr', -1),
        ('btc_eth', -1)
    )


class MyStrategy1(bt.Strategy):

    def log(self, txt, dt=None):
        dt = dt or self.datas[0].datetime.datetime(0)
        print('%s, %s' % (dt.isoformat(), txt))

    def __init__(self):
        self.btcclose = self.datas[0].close
        self.btcopen = self.datas[0].open
        self.btchigh = self.datas[0].high
        self.btclow = self.datas[0].low
        self.btcvolume = self.datas[0].volume
        self.corr = self.datas[0].corr
        self.ethclose = self.datas[1].close
        self.ethopen = self.datas[1].open
        self.ethhigh = self.datas[1].high
        self.ethlow = self.datas[1].low
        self.ethvolume = self.datas[1].volume
        # self.sma_1d_8 = bt.ind.SMA(self.btcclose, period=192)
        # self.sma_1d_13 = bt.ind.SMA(self.btcclose, period=312)
        # self.sma_1d_21 = bt.ind.SMA(self.btcclose, period=504)
        self.size1 = 50000 / self.btcclose
        self.size2 = 50000 / self.ethclose
        self.time = self.datas[0].datetime
        self.z_score1 = self.datas[0].z_score
        self.z_score2 = self.datas[1].z_score
        self.t_score1 = self.datas[0].t_score
        self.t_score2 = self.datas[1].t_score
        self.spread_z_score = self.z_score1 - self.z_score2
        self.spread_t_score = self.t_score1 - self.t_score2
        self.btc_eth = self.datas[0].btc_eth
        # self.sma24 = bt.ind.SMA(self.btcclose, period=24)
        # self.sma72 = bt.ind.SMA(self.btcclose, period=72)
        # self.sma168 = bt.ind.SMA(self.btcclose, period=168)
        # self.long_signal = bt.And(self.sma_1d_8 > self.sma_1d_13,  self.sma_1d_13 > self.sma_1d_21)
        self.order = None
        self.trade_count = 0
        self.win_count = 0
        self.buy_price = 0
        self.sell_price = 0
        self.win_rate = 0
        self.first_value = 100000
        self.last_value = 0
        self.win_value_list = []
        self.loss_value_list = []
        self.return_ = 0
        self.win_return_list = []
        self.loss_return_list = []
        self.pnl_count = pd.DataFrame(columns=['date', 'orientation', 'profit'])

    def notify_order(self, order):
        d1 = self.getdatabyname('btc')
        d2 = self.getdatabyname('eth')
        if order.status in [order.Submitted, order.Accepted]:
            # broker 提交/接受了，买/卖订单则什么都不做
            return
        # 检查一个订单是否完成
        # 注意: 当资金不足时，broker会拒绝订单
        if order.status in [order.Completed]:
            if order.isbuy() and self.getposition(d1).size == 0:
                self.log(
                    '已平空, 价格: %.2f, 费用: %.2f, 佣金 %.2f' %
                    (
                     order.executed.price,
                     order.executed.value,
                     order.executed.comm,
                     ))

            elif order.issell() and self.getposition().size == 0:
                self.log('已平多, 价格: %.2f, 费用: %.2f, 佣金 %.2f, ' %
                         (
                          order.executed.price,
                          order.executed.value,
                          order.executed.comm,
                          ))

            # 记录当前交易数量
            self.bar_executed = len(self)

        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('订单取消/保证金不足/拒绝')

        # 其他状态记录为：无挂起订单
        self.order = None

        # 交易状态通知，一买一卖算交易

    def notify_trade(self, trade):
        if not trade.isclosed:
            return
        elif self.broker.get_value() >= self.last_value:
            self.return_ = (self.broker.get_value() / self.last_value - 1) * 100
            self.log('市值 %.2f, 毛利润 %.2f, 净利润 %.2f, 收益率 %.2f %%, 回撤为 -%.2f%%' %
                     (self.broker.get_value(),
                      trade.pnl,
                      trade.pnlcomm,
                      self.return_,
                      self.stats.drawdown.drawdown[0],
                      ))
        else:
            self.return_ = (self.broker.get_value() / self.last_value - 1) * 100
            self.log('市值 %.2f, 毛利润 %.2f, 净利润 %.2f, 收益率 %.2f %%, 回撤为 -%.2f%%' %
                     (self.broker.get_value(),
                      trade.pnl,
                      trade.pnlcomm,
                      self.return_,
                      self.stats.drawdown.drawdown[0],
                      ))

    def start(self):
        self.mystats = open('mystats.csv', 'wb')
        self.mystats.write(str.encode('datetime,drawdown,maxdrawdown\n'))

    def next(self):
        self.mystats.write(str.encode(self.data.datetime.datetime(-1).strftime('%Y-%m-%d %H:%M:%S')))
        self.mystats.write(str.encode(',%.2f' % self.stats.drawdown.drawdown[0]))
        self.mystats.write(str.encode(',%.2f' % self.stats.drawdown.maxdrawdown[0]))
        self.mystats.write(str.encode('\n'))
        d1 = self.getdatabyname('btc')
        d2 = self.getdatabyname('eth')
        if self.getposition(d1).size == 0 and self.getposition(d2).size == 0 and -20 <= self.spread_t_score[0] <= -6 and self.z_score2[0] > 0.2:# and self.corr[0] > 0.9:
            self.order1 = self.sell(data=d1, size=self.size1[0])
            self.order2 = self.buy(data=d2, size=self.size2[0])

            self.last_value = self.broker.get_value()
            self.trade_price = self.btc_eth[0]
            self.log('做空价差:{}'.format(self.spread_t_score[0]))
            self.log('btc价格:{}, eth价格:{}'.format(self.datas[0].close[0], self.datas[1].close[0]))

        if self.getposition(d1).size == 0 and self.getposition(d2).size == 0 and self.spread_t_score[0] <= -5.5 and self.z_score2[0] < -0.2:# and self.corr[0] > 0.9:
            self.order1 = self.buy(data=d1, size=self.size1[0])
            self.order2 = self.sell(data=d2, size=self.size2[0])

            self.last_value = self.broker.get_value()
            self.trade_price = self.btc_eth[0]
            self.log('xx做多价差:{}'.format(self.spread_t_score[0]))
            self.log('btc价格:{}, eth价格:{}'.format(self.datas[0].close[0], self.datas[1].close[0]))

        if self.getposition(d1).size == 0 and self.getposition(d2).size == 0 and self.spread_t_score[0] >= 5.5 and self.z_score1[0] > 0.2:# and self.corr[0] > 0.9:
            self.order1 = self.buy(data=d1, size=self.size1[0])
            self.order2 = self.sell(data=d2, size=self.size2[0])

            self.last_value = self.broker.get_value()
            self.trade_price = self.btc_eth[0]
            self.log('做多价差:{}'.format(self.spread_t_score[0]))
            self.log('btc价格:{}, eth价格:{}'.format(self.datas[0].close[0], self.datas[1].close[0]))

        if self.getposition(d1).size == 0 and self.getposition(d2).size == 0 and 20 >= self.spread_t_score[0] >= 6 and self.z_score1[0] < -0.2:#and self.corr[0] > 0.9:
            self.order1 = self.sell(data=d1, size=self.size1[0])
            self.order2 = self.buy(data=d2, size=self.size2[0])

            self.last_value = self.broker.get_value()
            self.trade_price = self.btc_eth[0]
            self.log('做空价差:{}'.format(self.spread_t_score[0]))
            self.log('btc价格:{}, eth价格:{}'.format(self.datas[0].close[0], self.datas[1].close[0]))

        elif self.getposition(d1).size < 0 and self.getposition(d2).size > 0 and self.spread_z_score[0] >= 0:  # + 0.5*self.std:
            self.order1 = self.close(data=d1)
            self.order2 = self.close(data=d2)
            self.last_value = self.broker.get_value()
            self.log('平空价差:{}'.format(self.spread_t_score[0]))

        elif self.getposition(d1).size > 0 and self.getposition(d2).size < 0 and self.spread_z_score[0] <= 0:  # - 0.5*self.std:
            self.order1 = self.close(data=d1)
            self.order2 = self.close(data=d2)
            self.last_value = self.broker.get_value()
            self.log('平多价差:{}'.format(self.spread_z_score[0]))

        elif self.getposition(d1).size > 0 and 1 - self.btc_eth[0] / self.trade_price > 0.05:
            self.order1 = self.close(data=d1)
            self.order2 = self.close(data=d2)
            self.last_value = self.broker.get_value()
            self.log('止损平多: btc_eth {}'.format(self.btc_eth[0]))

        elif self.getposition(d1).size < 0 and self.btc_eth[0] / self.trade_price - 1 > 0.05:
            self.order1 = self.close(data=d1)
            self.order2 = self.close(data=d2)
            self.last_value = self.broker.get_value()
            self.log('止损平空: btc_eth {}'.format(self.btc_eth[0]))

        elif self.getposition(d1).size > 0 and self.btc_eth[0] / self.trade_price - 1 > 0.12:
            self.order1 = self.close(data=d1)
            self.order2 = self.close(data=d2)
            self.last_value = self.broker.get_value()
            self.log('止盈平多: btc_eth {}'.format(self.btc_eth[0]))

        elif self.getposition(d1).size < 0 and 1 - self.btc_eth[0] / self.trade_price > 0.12:
            self.order1 = self.close(data=d1)
            self.order2 = self.close(data=d2)
            self.last_value = self.broker.get_value()
            self.log('止盈平空: btc_eth {}'.format(self.btc_eth[0]))

    def stop(self):
        self.mystats.write(str.encode(self.data.datetime.date(0).strftime('%Y-%m-%d')))
        self.mystats.write(str.encode(',%.2f' % self.stats.drawdown.drawdown[0]))
        self.mystats.write(str.encode(',%.2f' % self.stats.drawdown.maxdrawdown[0]))
        self.mystats.write(str.encode('\n'))


if __name__ == '__main__':
    # Create a cerebro entity
    cerebro = bt.Cerebro()

    # Add a strategy
    cerebro.addstrategy(MyStrategy1)

    # df = get_bars('btcusdt')
    df_btc = pd.read_csv(r'2020_2022_gate_btc_basis_1h.csv', parse_dates=True, index_col=0).loc[
             '2020-1-1': '2022-10-31']
    df_eth = pd.read_csv(r'2020_2022_gate_eth_basis_1h.csv', parse_dates=True, index_col=0).loc[
             '2020-1-1': '2022-10-31']
    df_btc = df_btc.resample('4h').agg({'close': 'last', 'open': 'first', 'high': 'max', 'low': 'min', 'volume': 'sum'})
    df_eth = df_eth.resample('4h').agg({'close': 'last', 'open': 'first', 'high': 'max', 'low': 'min', 'volume': 'sum'})
    df_btc.open = df_btc.open.astype("float")
    df_btc.high = df_btc.high.astype("float")
    df_btc.low = df_btc.low.astype("float")
    df_btc.close = df_btc.close.astype("float")
    df_btc.volume = df_btc.volume.astype("float")
    df_btc.loc[:, 'z_score'] = (df_btc.close - df_btc.close.rolling(45).mean()) / df_btc.close.rolling(50).std()
    df_btc.loc[:, 't_score'] = 50 + 10 * df_btc['z_score']
    df_btc = df_btc[['close', 'open', 'high', 'low', 'volume', 'z_score', 't_score']]
    df_btc = df_btc.drop_duplicates()

    df_eth.open = df_eth.open.astype("float")
    df_eth.high = df_eth.high.astype("float")
    df_eth.low = df_eth.low.astype("float")
    df_eth.close = df_eth.close.astype("float")
    df_eth.volume = df_eth.volume.astype("float")
    df_eth.loc[:, 'z_score'] = (df_eth.close - df_eth.close.rolling(45).mean()) / df_eth.close.rolling(50).std()
    df_eth.loc[:, 't_score'] = 50 + 10 * df_eth['z_score']
    df_eth = df_eth[['close', 'open', 'high', 'low', 'volume', 'z_score', 't_score']]
    df_eth = df_eth.drop_duplicates()

    df_btc.loc[:, 'corr'] = df_btc.close.rolling(14).mean().corr(df_eth.close.rolling(14).mean())
    df_btc.loc[:, 'btc_eth'] = df_btc.close / df_eth.close
    # df_btc.loc[:, 'upper'] = df_btc.btc_eth.rolling(42).mean() + df_btc.btc_eth.rolling(42).std()*2
    # df_btc.loc[:, 'lower'] = df_btc.btc_eth.rolling(42).mean() - df_btc.btc_eth.rolling(42).std()*2
    # df_btc.loc[:, 'mid'] = df_btc.btc_eth.rolling(42).mean()
    # df_btc.loc[:, 'btc_eth_mm'] = (df_btc.btc_eth - df_btc.btc_eth.rolling(720).min()) / (df_btc.btc_eth.rolling(720).max() - df_btc.btc_eth.rolling(720).min())
    # df_btc.loc[:, 'mm_spread'] = df_btc['mm'] - df_eth['mm']
    # df_btc.loc[:, 'btc_eth_sma3'] = df_btc['btc_eth'].rolling(3).mean()

    start = datetime(2020, 1, 1)
    end = datetime(2022, 10, 31)
    # end = datetime(2021, 9, 30)
    datafeed1 = PandasData_more(
        dataname=df_btc,
        close=0,
        open=1,
        high=2,
        low=3,
        volume=4,
        z_score=-1,
        t_score=-1,
        corr=-1,
        btc_eth=-1,
        fromdate=start,
        todate=end,
        # timeframe=bt.TimeFrame.Days,
    )

    datafeed2 = PandasData_more(
        dataname=df_eth,
        close=0,
        open=1,
        high=2,
        low=3,
        volume=4,
        z_score=-1,
        t_score=-1,
        fromdate=start,
        todate=end,
        # timeframe=bt.TimeFrame.Days,
    )

    # Add the Data Feed to Cerebro
    cerebro.adddata(datafeed1, name='btc')
    cerebro.adddata(datafeed2, name='eth')

    # Set our desired cash start
    cash = 100000.0
    cerebro.broker.setcash(cash)

    fee_rate = 0.0000
    cerebro.broker.setcommission(commission=fee_rate)

    print('开始市值: %.2f' % cerebro.broker.getvalue())

    cerebro.addanalyzer(btanalyzers.Transactions, _name="trans")
    cerebro.addanalyzer(btanalyzers.DrawDown, _name="DD")
    cerebro.addanalyzer(btanalyzers.AnnualReturn, _name="AR")
    cerebro.addanalyzer(btanalyzers.TradeAnalyzer, _name="trade")
    cerebro.addanalyzer(btanalyzers.Returns, _name="returns")

    cerebro.addobserver(bt.observers.DrawDown)
    cerebro.addobserver(bt.observers.Benchmark, data=datafeed1, timeframe=bt.TimeFrame.NoTimeFrame)
    cerebro.addobserver(bt.observers.Benchmark, data=datafeed2, timeframe=bt.TimeFrame.NoTimeFrame)
    cerebro.addanalyzer(bt.analyzers.PyFolio, _name='pyfolio')

    cerebro.addtz(tz)

    back = cerebro.run()

    pyfoliozer = back[0].analyzers.getbyname('pyfolio')
    returns, positions, transactions, gross_lev = pyfoliozer.get_pf_items()
    positions = pd.DataFrame(pyfoliozer.get_analysis().get('positions')).set_index('Datetime').T
    cumulative = (returns + 1).cumprod()
    max_return = cumulative.cummax()
    drawdown = (cumulative - max_return) / max_return

    df_drawdown = pd.read_csv('mystats.csv', parse_dates=True, index_col=0)
    max_drawdown_period = 0
    count = 0
    for i in df_drawdown.drawdown.values:
        if i == 0:
            count = 0
        else:
            count += 1
        if count > max_drawdown_period:
            max_drawdown_period = count

    u = np.log(returns + 1)
    s = math.sqrt(((u - u.mean()) ** 2).sum() / (len(u) - 1))

    sharpe_ratio = u.sum() / len(u) / s * math.sqrt(365)
    annual_return = (cumulative[-1] - 1) / len(cumulative) * 365
    log_ar = u.sum() / len(returns) * 365
    annual_volatility = s * math.sqrt(365)

    df_trade_records = transactions.reset_index()
    # df_trade_records.loc[:, 'pair'] = 'BTC'
    # df_trade_records.loc[:, 'Timestamp'] = df_trade_records['date'].apply(lambda x: time.mktime(x.timetuple()))
    # df_trade_records.loc[:, 'buy'] = df_trade_records.apply(lambda x: x['amount'] if x['value'] < 0 else 0, axis=1)
    # df_trade_records.loc[:, 'sell'] = df_trade_records.apply(lambda x: -x['amount'] if x['value'] > 0 else 0, axis=1)
    # df_trade_records.loc[:, 'scale'] = '1h'
    # df_trade_records.loc[:, 'stage'] = 'GATE'
    # df_trade_records[['Timestamp', 'buy', 'sell', 'pair', 'scale', 'stage', 'value']].to_excel(r'long_short_count.xlsx', index=None)

    date_list = []
    for i in range(len(df_trade_records)):
        for j in range(i, len(df_trade_records), 1):
            if df_trade_records['amount'][i] > 0 and df_trade_records['amount'][j] < 0:
                date_list.append((df_trade_records['date'][i], df_trade_records['date'][j]))
                break
            elif df_trade_records['amount'][i] < 0:
                break
            else:
                continue

    # df_trend = pd.DataFrame()
    # for i, j in date_list:
    #     c = df.close.loc[i: j].reset_index(drop=True)
    #     c = c / c.iloc[0] * 100
    #     df_trend = pd.concat([df_trend, c], axis=1)

    # ax = df_trend.plot(legend='')
    # fig = ax.get_figure()
    # fig.savefig(r'/Users/<USER>/Documents/time_trend.png', dpi=500)

    perf_ststs_year = returns.groupby(returns.index.to_period('y')).apply(
        lambda data: pf.timeseries.perf_stats(data)).unstack()

    perf_ststs_all = pf.timeseries.perf_stats(returns).to_frame(name='all')
    perf_stats = pd.concat([perf_ststs_year, perf_ststs_all.T], axis=0)
    perf_stats_ = round(perf_stats, 4).reset_index()

    print('回测日期：{} ~ {}'.format(start, end))
    print('开始市值： %.2f，最终市值: %.2f' % (cash, cerebro.broker.getvalue()))
    print('手续费率： %.2f %%' % (fee_rate * 100))
    print("总收益率: %.2f %%" % ((cerebro.broker.getvalue() / cash - 1) * 100))
    print('年化收益率: %.2f %%' % (annual_return * 100))
    print('对数年化收益率: %f ' % log_ar)
    print("最大回撤: %.2f %%" % back[0].analyzers.DD.get_analysis().max.drawdown)
    print('交易次数： %d' % (len(transactions) / 2))
    print('交易次数（年）： %d' % (len(transactions) / 2 / len(returns) * 365))
    print('夏普比例：%f ' % sharpe_ratio)
    print('年化波动率： %f' % annual_volatility)





